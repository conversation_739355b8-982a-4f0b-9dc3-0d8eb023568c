<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org" lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>My Prescriptions</title>
    <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;700&display=swap" rel="stylesheet">
    <style>
        body {
            font-family: 'Roboto', sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f4f4f4;
        }
        h1 {
            text-align: center;
            color: #2c3e50;
            margin-bottom: 30px;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            background-color: white;
            box-shadow: 0 1px 3px rgba(0,0,0,0.2);
        }
        th, td {
            padding: 12px 15px;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }
        th {
            background-color: #3498db;
            color: white;
            font-weight: bold;
            text-transform: uppercase;
        }
        tr:hover {
            background-color: #f5f5f5;
        }
        .status {
            font-weight: bold;
            padding: 5px 10px;
            border-radius: 20px;
            display: inline-block;
        }
        .status-PENDING { background-color: #ffeaa7; color: #d35400; }
        .status-AVAILABLE { background-color: #55efc4; color: #00b894; }
        .status-FULFILLED { background-color: #81ecec; color: #00cec9; }
        .status-EXPIRED { background-color: #fab1a0; color: #d63031; }
        @media screen and (max-width: 600px) {
            table {
                font-size: 14px;
            }
            th, td {
                padding: 8px 10px;
            }
        }
    </style>
</head>
<body>
<h1>My Prescriptions</h1>
<table>
    <thead>
    <tr>
        <th>Drug Name</th>
        <th>Status</th>
        <th>Prescribed Date</th>
    </tr>
    </thead>
    <tbody>
    <tr th:each="prescription : ${prescriptions}">
        <td th:text="${prescription.drugName}"></td>
        <td>
                    <span th:text="${prescription.status}"
                          th:class="'status status-' + ${prescription.status}">
                    </span>
        </td>
        <td th:text="${#temporals.format(prescription.prescriptionDate, 'dd MMM yyyy')}"></td>
    </tr>
    </tbody>
</table>
</body>
</html>