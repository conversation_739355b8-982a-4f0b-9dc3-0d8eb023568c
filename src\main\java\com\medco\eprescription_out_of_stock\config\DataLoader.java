package com.medco.eprescription_out_of_stock.config;

import com.medco.eprescription_out_of_stock.Entitiy.User.Privilege;
import com.medco.eprescription_out_of_stock.Entitiy.User.Role;
import com.medco.eprescription_out_of_stock.Entitiy.User.RolePrivilege;
import com.medco.eprescription_out_of_stock.Entitiy.User.User;
import com.medco.eprescription_out_of_stock.Repository.Users.PrivilegeRepository;
import com.medco.eprescription_out_of_stock.Repository.Users.RolePrivilegeRepository;
import com.medco.eprescription_out_of_stock.Repository.Users.RoleRepository;
import com.medco.eprescription_out_of_stock.Repository.Users.UserRepository;
import com.medco.eprescription_out_of_stock.shared.enums.Status;
import com.medco.eprescription_out_of_stock.shared.enums.UserStatus;
import com.medco.eprescription_out_of_stock.shared.enums.UserType;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.CommandLineRunner;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;

@Component
public class DataLoader implements CommandLineRunner {

    @Autowired
    private UserRepository userRepository;

    @Autowired
    private RoleRepository roleRepository;

    @Autowired
    private PrivilegeRepository privilegeRepository;

    @Autowired
    private RolePrivilegeRepository rolePrivilegeRepository;

    @Autowired
    private PasswordEncoder passwordEncoder;

    private static final String SUPER_ADMIN_EMAIL = "<EMAIL>";

    @Override
    @Transactional
    public void run(String... args) {
        loadPrivileges();
        loadRoles();
        loadUsers();
    }

    private void loadPrivileges() {
        List<String> privilegeNames = Arrays.asList(
                // User Management APIs
                "CREATE_USER", "READ_USER", "UPDATE_USER", "DELETE_USER", "VIEW_USER",
                "SIGN_IN_USER", "SIGN_UP_USER", "RESET_PASSWORD_USER", "CHANGE_PASSWORD_USER",
                "VERIFY_ACCOUNT_USER", "CHECK_RESET_CODE_USER", "SEARCH_USERS",

                // Role Management APIs
                "CREATE_ROLE", "READ_ROLE", "UPDATE_ROLE", "DELETE_ROLE", "SEARCH_ROLES",
                "ADD_ROLE_PRIVILEGES", "DELETE_ROLE_PRIVILEGES",

                // Privilege Management APIs
                "CREATE_PRIVILEGE", "READ_PRIVILEGE", "UPDATE_PRIVILEGE", "DELETE_PRIVILEGE",
                "LIST_PRIVILEGES", "SEARCH_PRIVILEGES",

                // Prescription APIs
                "CREATE_PRESCRIPTION", "READ_PRESCRIPTION", "UPDATE_PRESCRIPTION", "DELETE_PRESCRIPTION",
                "SEARCH_PRESCRIPTIONS", "GET_PRESCRIPTIONS_BY_PHARMACY", "GET_PRESCRIPTIONS_BY_PATIENT",
                "LOG_PRESCRIPTION", "GET_ALL_PRESCRIPTIONS",

                // Prescription Out of Stock APIs
                "CREATE_OUT_OF_STOCK_REQUEST", "READ_OUT_OF_STOCK_REQUEST", "UPDATE_OUT_OF_STOCK_STATUS",
                "SEARCH_OUT_OF_STOCK_PRESCRIPTIONS", "ADVANCED_SEARCH_OUT_OF_STOCK",
                "GET_PATIENT_PRESCRIPTIONS", "PROCESS_INCOMING_PRESCRIPTION", "TEST_LOCATION_GROUPING",

                // Drug Management APIs
                "CREATE_DRUG", "READ_DRUG", "UPDATE_DRUG", "DELETE_DRUG", "SEARCH_DRUGS", "GET_ALL_DRUGS",

                // Pharmacy Management APIs
                "CREATE_PHARMACY", "READ_PHARMACY", "UPDATE_PHARMACY", "DELETE_PHARMACY", "GET_ALL_PHARMACIES",

                // Institution Management APIs
                "CREATE_INSTITUTION", "READ_INSTITUTION", "UPDATE_INSTITUTION", "DELETE_INSTITUTION",
                "GET_ALL_INSTITUTIONS", "IMPORT_INSTITUTIONS",

                // Branch Management APIs
                "READ_BRANCH", "DELETE_BRANCH", "GET_ALL_BRANCHES",

                // Dispense Management APIs
                "DISPENSE_DRUG", "READ_DISPENSED_DRUG", "UPDATE_DISPENSED_DRUG", "DELETE_DISPENSED_DRUG", "GET_ALL_DISPENSED_DRUGS",

                // Inventory Management APIs
                "CREATE_INVENTORY", "READ_INVENTORY", "UPDATE_INVENTORY", "DELETE_INVENTORY",
                "GET_PHARMACY_INVENTORY", "CHECK_AVAILABILITY_INVENTORY",

                // Patient APIs
                "GET_PATIENT_PRESCRIPTIONS",

                // Returned Prescriptions APIs
                "CREATE_RETURNED_PRESCRIPTION", "READ_RETURNED_PRESCRIPTION", "UPDATE_RETURNED_PRESCRIPTION",
                "DELETE_RETURNED_PRESCRIPTION", "GET_ALL_RETURNED_PRESCRIPTIONS",

                // External System Audit APIs
                "SEARCH_AUDIT_LOGS", "GET_AUDIT_BY_PRESCRIPTION", "GET_AUDIT_BY_PATIENT",
                "GET_AUDIT_BY_DATE_RANGE", "GET_SYSTEM_STATISTICS", "GET_HEALTH_METRICS",
                "GET_FAILED_REQUESTS", "GET_AUDIT_BY_ID", "GET_SYSTEM_TYPES", "GET_AUDIT_STATUSES",
                "GET_DASHBOARD_SUMMARY",

                // Physician/Pharmacist Registration APIs
                "SIGN_UP_PHYSICIAN", "SIGN_UP_PHARMACIST", "SIGN_UP_PATIENT",

                // Legacy APIs (keeping for backward compatibility)
                "Delete Drugs", "Update Drugs", "Create Drugs",
                "Delete Services", "Update Services", "Create Services", "CREATE_SERVICE"
        );

        for (String name : privilegeNames) {
            if (privilegeRepository.findByPrivilegeName(name) == null) {
                Privilege privilege = new Privilege();
                privilege.setPrivilegeName(name);
                privilege.setPrivilegeDescription("Allows " + name.toLowerCase().replace("_", " "));
                privilege.setPrivilegeCategory("API_ACCESS");
                privilege.setPrivilegeType("FOR_SYSTEM_ADMIN");
                privilegeRepository.save(privilege);
                System.out.println("Created privilege: " + name);
            }
        }
    }

    @Transactional
    private void loadRoles() {
        Role superAdminRole = roleRepository.findByRoleName("ROLE_SUPER_ADMIN");

        if (superAdminRole == null) {
            superAdminRole = new Role();
            superAdminRole.setRoleName("ROLE_SUPER_ADMIN");
            superAdminRole.setRoleDescription("Super Administrator Role");
            roleRepository.save(superAdminRole);
            System.out.println("Created ROLE_SUPER_ADMIN");
        }

        List<String> superAdminPrivilegeNames = Arrays.asList(
                // User Management APIs
                "CREATE_USER", "READ_USER", "UPDATE_USER", "DELETE_USER", "VIEW_USER",
                "SIGN_IN_USER", "SIGN_UP_USER", "RESET_PASSWORD_USER", "CHANGE_PASSWORD_USER",
                "VERIFY_ACCOUNT_USER", "CHECK_RESET_CODE_USER", "SEARCH_USERS",

                // Role Management APIs
                "CREATE_ROLE", "READ_ROLE", "UPDATE_ROLE", "DELETE_ROLE", "SEARCH_ROLES",
                "ADD_ROLE_PRIVILEGES", "DELETE_ROLE_PRIVILEGES",

                // Privilege Management APIs
                "CREATE_PRIVILEGE", "READ_PRIVILEGE", "UPDATE_PRIVILEGE", "DELETE_PRIVILEGE",
                "LIST_PRIVILEGES", "SEARCH_PRIVILEGES",

                // Prescription APIs
                "CREATE_PRESCRIPTION", "READ_PRESCRIPTION", "UPDATE_PRESCRIPTION", "DELETE_PRESCRIPTION",
                "SEARCH_PRESCRIPTIONS", "GET_PRESCRIPTIONS_BY_PHARMACY", "GET_PRESCRIPTIONS_BY_PATIENT",
                "LOG_PRESCRIPTION", "GET_ALL_PRESCRIPTIONS",

                // Prescription Out of Stock APIs
                "CREATE_OUT_OF_STOCK_REQUEST", "READ_OUT_OF_STOCK_REQUEST", "UPDATE_OUT_OF_STOCK_STATUS",
                "SEARCH_OUT_OF_STOCK_PRESCRIPTIONS", "ADVANCED_SEARCH_OUT_OF_STOCK",
                "GET_PATIENT_PRESCRIPTIONS", "PROCESS_INCOMING_PRESCRIPTION", "TEST_LOCATION_GROUPING",

                // Drug Management APIs
                "CREATE_DRUG", "READ_DRUG", "UPDATE_DRUG", "DELETE_DRUG", "SEARCH_DRUGS", "GET_ALL_DRUGS",

                // Pharmacy Management APIs
                "CREATE_PHARMACY", "READ_PHARMACY", "UPDATE_PHARMACY", "DELETE_PHARMACY", "GET_ALL_PHARMACIES",

                // Institution Management APIs
                "CREATE_INSTITUTION", "READ_INSTITUTION", "UPDATE_INSTITUTION", "DELETE_INSTITUTION",
                "GET_ALL_INSTITUTIONS", "IMPORT_INSTITUTIONS",

                // Branch Management APIs
                "READ_BRANCH", "DELETE_BRANCH", "GET_ALL_BRANCHES",

                // Dispense Management APIs
                "DISPENSE_DRUG", "READ_DISPENSED_DRUG", "UPDATE_DISPENSED_DRUG", "DELETE_DISPENSED_DRUG", "GET_ALL_DISPENSED_DRUGS",

                // Inventory Management APIs
                "CREATE_INVENTORY", "READ_INVENTORY", "UPDATE_INVENTORY", "DELETE_INVENTORY",
                "GET_PHARMACY_INVENTORY", "CHECK_AVAILABILITY_INVENTORY",

                // Patient APIs
                "GET_PATIENT_PRESCRIPTIONS",

                // Returned Prescriptions APIs
                "CREATE_RETURNED_PRESCRIPTION", "READ_RETURNED_PRESCRIPTION", "UPDATE_RETURNED_PRESCRIPTION",
                "DELETE_RETURNED_PRESCRIPTION", "GET_ALL_RETURNED_PRESCRIPTIONS",

                // External System Audit APIs
                "SEARCH_AUDIT_LOGS", "GET_AUDIT_BY_PRESCRIPTION", "GET_AUDIT_BY_PATIENT",
                "GET_AUDIT_BY_DATE_RANGE", "GET_SYSTEM_STATISTICS", "GET_HEALTH_METRICS",
                "GET_FAILED_REQUESTS", "GET_AUDIT_BY_ID", "GET_SYSTEM_TYPES", "GET_AUDIT_STATUSES",
                "GET_DASHBOARD_SUMMARY",

                // Physician/Pharmacist Registration APIs
                "SIGN_UP_PHYSICIAN", "SIGN_UP_PHARMACIST", "SIGN_UP_PATIENT"
        );

        for (String privilegeName : superAdminPrivilegeNames) {
            Privilege privilege = privilegeRepository.findByPrivilegeName(privilegeName);
            if (privilege != null) {
                boolean exists = rolePrivilegeRepository.existsByRoleAndPrivilege(superAdminRole, privilege);
                if (!exists) {
                    RolePrivilege rolePrivilege = new RolePrivilege();
                    rolePrivilege.setRole(superAdminRole);
                    rolePrivilege.setPrivilege(privilege);
                    rolePrivilegeRepository.save(rolePrivilege);
                    System.out.println("Linked " + privilegeName + " to ROLE_SUPER_ADMIN");
                }
            }
        }
    }

    private void loadUsers() {
        User existingSuperAdmin = userRepository.findByEmail(SUPER_ADMIN_EMAIL);

        Role superAdminRole = roleRepository.findByRoleName("ROLE_SUPER_ADMIN");
        if (superAdminRole == null) {
            throw new RuntimeException("ROLE_SUPER_ADMIN not found. Ensure roles are loaded before users.");
        }

        if (existingSuperAdmin == null) {
            User superAdmin = new User();
            superAdmin.setEmail(SUPER_ADMIN_EMAIL);
            superAdmin.setPassword(passwordEncoder.encode("passme"));
            superAdmin.setTitle("Mr.");
            superAdmin.setFirstName("Super");
            superAdmin.setFatherName("Admin");
            superAdmin.setGrandFatherName("System");
            superAdmin.setGender("male");
            superAdmin.setMobilePhone("1234567890");
            superAdmin.setStatus(Status.ACTIVE);
            superAdmin.setUserStatus(UserStatus.ACTIVE);
            superAdmin.setUserType(UserType.ADMIN);

            superAdmin.setRoleName(superAdminRole.getRoleName());
            superAdmin.setRoleUuid(superAdminRole.getRoleUuid());
            superAdmin.getRoles().add(superAdminRole);
            superAdminRole.setUser(superAdmin);

            userRepository.save(superAdmin);
            System.out.println("Super Admin user created successfully.");
        } else {
            existingSuperAdmin.setRoleName(superAdminRole.getRoleName());
            existingSuperAdmin.setRoleUuid(superAdminRole.getRoleUuid());

            if (!existingSuperAdmin.getRoles().contains(superAdminRole)) {
                existingSuperAdmin.getRoles().add(superAdminRole);
            }
            superAdminRole.setUser(existingSuperAdmin);

            userRepository.save(existingSuperAdmin);
            System.out.println("Super Admin user already exists. Updated role info if necessary.");
        }
    }
}
