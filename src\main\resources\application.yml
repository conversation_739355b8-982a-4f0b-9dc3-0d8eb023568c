spring:
  application:
    name: eprescription-out-of-stock

  datasource:
    url: ${DB_URL:************************************}
    username: ${DB_USERNAME:postgres}
    password: ${DB_PASSWORD:postgres}

  jpa:
    hibernate:
      ddl-auto: update
    properties:
      hibernate:
        dialect: org.hibernate.dialect.PostgreSQLDialect

  app:
    jwtSecret: abcdefghijKLMNOPQRSTUvwxyz=====LEMOBILE====0123456987
    jwtExpirationMs:  21600000
    refreshExpirationMs: 18000000

file:
  dir-attachments:  C:/Users/<USER>/OneDrive/Desktop/erxFile

afromessage:
  api:
    url: "https://api.afromessage.com/api/send"
    token: eyJhbGciOiJIUzI1NiJ9.eyJpZGVudGlmaWVyIjoiWG5RMHRYdERVcU8yQXBVQ1ZuWGpxODVkQTB4dHVNYnciLCJleHAiOjE4OTMzOTg3MTEsImlhdCI6MTczNTYzMjMxMSwianRpIjoiMDE5YTNlNWUtNTgyMS00MzhjLTlkMDYtYThkMjI2ZDJkMDQ0In0.OmziYU_5wHCm53w9AKzakoye_OFpPUFTQOJxwkXF8jw
    identifierId: e80ad9d8-adf3-463f-80f4-7c4b39f7f164

kenema:
  api:
    url: https://cfb.kenemapharmacy.com

#kenema:
#  api:
#    url: http://*************:8990

servlet:
  multipart:
    enabled: true
    max-file-size: 100MB
    max-request-size: 120MB
    file-size-threshold: 2MB
    resolve-lazily: true

springdoc:
  api-docs:
    path: /v3/api-docs
    enabled: true
  swagger-ui:
    path: /swagger-ui.html
    enabled: true

server:
  port: ${$APP_PORT:3021}

api:
  key: hc_7f9a3b2e4d5c1f8e6a0d9b7c5e3f1a2d
