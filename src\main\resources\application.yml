spring:
  application:
    name: eprescription-out-of-stock

  datasource:
    url: ************************************
    username: postgres
    password: postgres

  jpa:
    hibernate:
      ddl-auto: update
    properties:
      hibernate:
        dialect: org.hibernate.dialect.PostgreSQLDialect

  app:
    jwtSecret: abcdefghijKLMNOPQRSTUvwxyz=====LEMOBILE====0123456987
    jwtExpirationMs:  21600000
    refreshExpirationMs: 18000000

file:
  dir-attachments:  /home/<USER>/Documents/Medco/lemobile/attachments

afromessage:
  api:
    url: "https://api.afromessage.com/api/send"
    token: eyJhbGciOiJIUzI1NiJ9.eyJpZGVudGlmaWVyIjoiOWVNWDc5YTVTNnZiTkdGVWlPUzNOZU1XYkVQYXVyS08iLCJleHAiOjE5MTEwMjIxNDIsImlhdCI6MTc1MzI1NTc0MiwianRpIjoiNjAwZDBlOTItZDAzNy00YmJlLWJiODItNDZiNjkwOTVjZDM0In0.gu6b0nl3G1NPWIp9E27LxYYIUENMOYzZvn19U82EnBg
    identifierId: e80ad9d8-adf3-463f-80f4-7c4b39f7f164
kenema:
  api:
    url: http://*************:8990

servlet:
  multipart:
    enabled: true
    max-file-size: 100MB
    max-request-size: 120MB
    file-size-threshold: 2MB
    resolve-lazily: true

springdoc:
  api-docs:
    path: /v3/api-docs
    enabled: true
  swagger-ui:
    path: /swagger-ui.html
    enabled: true

server:
  port: 8300

api:
  key: hc_7f9a3b2e4d5c1f8e6a0d9b7c5e3f1a2d